#!/usr/bin/env python3
"""
Test script for MCQ generation API
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_mcq_generation():
    """Test the MCQ generation for exam endpoint"""
    print("Testing MCQ generation for exam...")
    
    # Test data - using subject 3 (Biology) which has no questions
    test_data = {
        "subject_id": 3,  # Biology subject
        "num_questions": 5,
        "use_pdf_content": False
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/ai/generate-mcq-for-exam",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"Status Code: {response.status_code}")
        try:
            response_json = response.json()
            print(f"Response: {json.dumps(response_json, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing MCQ generation: {e}")
        return False

def test_questions_api():
    """Test fetching questions for a subject"""
    print("\nTesting questions API...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/questions/subject/1?count=10")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing questions API: {e}")
        return False

if __name__ == "__main__":
    print("Starting MCQ API tests...")
    
    # Test MCQ generation
    mcq_success = test_mcq_generation()
    
    # Test questions fetching
    questions_success = test_questions_api()
    
    print(f"\nResults:")
    print(f"MCQ Generation: {'✓' if mcq_success else '✗'}")
    print(f"Questions API: {'✓' if questions_success else '✗'}")
