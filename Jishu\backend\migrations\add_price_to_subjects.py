#!/usr/bin/env python3
"""
Migration script to add price and currency columns to subjects table
"""

import sqlite3
import os
from decimal import Decimal

def add_price_columns():
    """Add price and currency columns to subjects table"""
    
    # Get the database path
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'app.db')
    
    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(subjects)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'price' not in columns:
            print("Adding price column to subjects table...")
            cursor.execute("ALTER TABLE subjects ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00")
            print("✓ Price column added successfully")
        else:
            print("Price column already exists")
            
        if 'currency' not in columns:
            print("Adding currency column to subjects table...")
            cursor.execute("ALTER TABLE subjects ADD COLUMN currency VARCHAR(3) DEFAULT 'INR'")
            print("✓ Currency column added successfully")
        else:
            print("Currency column already exists")
        
        # Set default prices for existing subjects based on category
        print("Setting default prices for existing subjects...")
        
        # Get all subjects with their categories
        cursor.execute("""
            SELECT s.id, s.subject_name, ec.name as category_name 
            FROM subjects s 
            JOIN exam_categories ec ON s.exam_category_id = ec.id
        """)
        subjects = cursor.fetchall()
        
        # Define default prices based on category and subject type
        price_mapping = {
            'NEET': {
                'Physics': 299.00,
                'Chemistry': 299.00,
                'Biology': 299.00,
                'Botany': 199.00,
                'Zoology': 199.00,
                'Full Mock': 499.00,
                'default': 249.00
            },
            'JEE': {
                'Physics': 349.00,
                'Chemistry': 349.00,
                'Mathematics': 349.00,
                'Full Mock': 599.00,
                'default': 299.00
            },
            'GATE': {
                'Computer Science': 399.00,
                'Electronics': 399.00,
                'Mechanical': 399.00,
                'Civil': 399.00,
                'Full Mock': 699.00,
                'default': 349.00
            }
        }
        
        for subject_id, subject_name, category_name in subjects:
            # Determine price based on category and subject name
            category_prices = price_mapping.get(category_name, {})
            
            # Check for specific subject matches
            price = None
            for key, value in category_prices.items():
                if key.lower() in subject_name.lower():
                    price = value
                    break
            
            # Use default price if no specific match found
            if price is None:
                price = category_prices.get('default', 199.00)
            
            # Update the subject with the determined price
            cursor.execute("""
                UPDATE subjects 
                SET price = ?, currency = 'INR' 
                WHERE id = ?
            """, (price, subject_id))
            
            print(f"✓ Set price for {subject_name} ({category_name}): INR {price}")
        
        conn.commit()
        print(f"\n✅ Migration completed successfully!")
        print(f"Updated {len(subjects)} subjects with pricing information")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if conn:
            conn.close()

def verify_migration():
    """Verify that the migration was successful"""
    
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'app.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check table structure
        cursor.execute("PRAGMA table_info(subjects)")
        columns = {column[1]: column[2] for column in cursor.fetchall()}
        
        print("\n📋 Current subjects table structure:")
        for col_name, col_type in columns.items():
            print(f"  - {col_name}: {col_type}")
        
        # Check if price and currency columns exist
        if 'price' in columns and 'currency' in columns:
            print("\n✅ Price and currency columns exist")
            
            # Show sample data
            cursor.execute("""
                SELECT s.subject_name, ec.name as category_name, s.price, s.currency
                FROM subjects s 
                JOIN exam_categories ec ON s.exam_category_id = ec.id
                LIMIT 5
            """)
            
            print("\n📊 Sample pricing data:")
            for row in cursor.fetchall():
                subject_name, category_name, price, currency = row
                print(f"  - {subject_name} ({category_name}): {currency} {price}")
                
        else:
            print("\n❌ Price or currency columns missing")
            
    except Exception as e:
        print(f"❌ Error verifying migration: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 Starting subjects pricing migration...")
    
    if add_price_columns():
        verify_migration()
    else:
        print("❌ Migration failed")
